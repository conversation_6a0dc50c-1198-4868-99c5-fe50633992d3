import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  NFC_READER,
  SENSOR_BUS,
  DISPLAY,
  LED_CONTROL,
} from '../../hardware/tokens';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { SensorBusService } from '../../hardware/interfaces/sensor-bus.interface';
import { DisplayService } from '../../hardware/interfaces/display.interface';
import {
  LedControlService,
  LedColor,
} from '../../hardware/interfaces/led-control.interface';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, Subscription } from 'rxjs';
import { ApiService } from '../../api/api.service';
import { DisplayGateway } from 'src/ui/display.gateway';
import { ControllinoSensorService } from '../../hardware/sensors/controllino-sensor.service';
import { TeamScoreRequest, PlayerScoreData } from '../../common/dto/game‑manager.dto';

// Interface for controllino score response
interface ControllinoScoreResponse {
  [badgeId: string]: number; // Dynamic mapping: badgeId -> score
}

@Injectable()
export class GameEngineService {
  private readonly log = new Logger(GameEngineService.name);
  private arcadeMachineOutput: string;
  private scoreSubscription?: Subscription;

  constructor(
    @Inject(NFC_READER) private nfc: NfcReaderService,
    @Inject(SENSOR_BUS) private sensors: SensorBusService,
    @Inject(DISPLAY) private display: DisplayService,
    @Inject(LED_CONTROL) private led: LedControlService,
    private cfg: ConfigService,
    private api: ApiService,
    private ui: DisplayGateway,
  ) {
    // Get arcade machine output mapping from config
    this.arcadeMachineOutput = this.cfg.get<string>(
      'global.hardware.controllino.outputs.arcadeMachine',
      '04'
    );

    this.log.log(`Arcade machine output mapping: ${this.arcadeMachineOutput}`);
    this.run();
  }

  private async run() {
    this.display.showSplash('Scan your team badge');
    // Set green LED while waiting for team
    this.led.setColor(LedColor.GREEN);
    const tag = await firstValueFrom(this.nfc.onTag());
    const gameId = this.cfg.get<number>('global.gameId') ?? 1;

    const adminBadges = this.cfg.get<string[]>('global.adminBadges') ?? [];
    const isAdmin = adminBadges.includes(tag);

    let teamData: any;
    let players: any[] = [];

    if (isAdmin) {
      this.log.warn(`Admin badge '${tag}' detected – bypassing API`);
      teamData = { id: 0, name: 'Admin Team' };
      players = [
        { id: 1, displayName: 'Admin Player 1' },
        { id: 2, displayName: 'Admin Player 2' },
        { id: 3, displayName: 'Admin Player 3' },
        { id: 4, displayName: 'Admin Player 4' }
      ];
    } else {
      const auth = await this.api.authorizeTeam(tag, gameId).catch(() => {});
      if (!auth || auth.code !== 200 || !auth.team || !auth.players) {
        this.display.showResult('🚫 Team Unauthorized');
        return this.run();
      }
      teamData = auth.team;
      players = auth.players;
    }

    this.log.log(`Team authorized: ${teamData.name} with ${players.length} players`);

    // Start the arcade game
    await this.startArcadeGame(teamData, players, isAdmin, gameId);
  }

  private async startArcadeGame(teamData: any, players: any[], isAdmin: boolean, gameId: number) {
    // Send team authorization to WebSocket clients and start session tracking
    const websocketDisplay = this.display as any;
    if (websocketDisplay.sendTeamAuthorized) {
      await websocketDisplay.sendTeamAuthorized(teamData);
    }

    // Set yellow LED and send team setup to controllino
    this.led.setColor(LedColor.YELLOW);
    this.sendTeamSetupCommand(players);
    this.sendArcadeMachineCommand(true);
    this.display.showSplash('Game in progress...');

    try {
      // Wait for controllino to send score response
      const scores = await this.waitForControllinoScores(players);

      // Turn off arcade machine and set blue LED
      this.sendArcadeMachineCommand(false);
      this.led.setColor(LedColor.BLUE);

      this.log.log(`Game finished - scores received: ${JSON.stringify(scores)}`);

      // Submit team scores to API (if not admin)
      if (!isAdmin) {
        await this.submitTeamScores(gameId, players, scores);
      }

      // Send game completion to WebSocket clients
      const websocketDisplay = this.display as any;
      if (websocketDisplay.sendGameComplete) {
        const scoreArray = Object.entries(scores).map(([badgeId, points]) => ({
          badgeId,
          points,
          player: players.find(p => p.badgeId === badgeId)
        }));
        websocketDisplay.sendGameComplete(scoreArray);
      } else {
        this.display.showResult(`Game Complete! Scores: ${JSON.stringify(scores)}`);
      }

      // Show success LED for 3 seconds
      await firstValueFrom(this.led.setColorForDuration(LedColor.GREEN, 3000));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Game error: ${errorMessage}`);
      this.sendArcadeMachineCommand(false); // Ensure machine is turned off
      this.led.setColor(LedColor.RED);
      this.display.showResult('Game Error - Please try again');

      // Show error LED for 3 seconds
      await firstValueFrom(this.led.setColorForDuration(LedColor.RED, 3000));
    }

    // Wait before restarting
    await new Promise((resolve) => setTimeout(resolve, 3000));
    return this.run(); // restart
  }

  private sendTeamSetupCommand(players: any[]): void {
    // Create team setup command: T<PLAYER_COUNT>:<BADGE1>,<BADGE2>,<BADGE3>,...
    const playerCount = players.length;
    const badgeIds = players.map(player => player.badgeId).join(',');
    const command = `T${playerCount}:${badgeIds}`;

    this.log.log(`Sending team setup command: ${command}`);
    this.log.log(`Team has ${playerCount} players: ${badgeIds}`);

    // Get the controllino sensor service to send the command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      controllinoSensor.sendCommand(command);
    } else {
      this.log.warn('Cannot send team setup command - sensor service does not support sendCommand');
    }
  }

  private sendArcadeMachineCommand(turnOn: boolean): void {
    const command = `O${this.arcadeMachineOutput}${turnOn ? '1' : '0'}`;
    this.log.log(`Sending arcade machine command: ${command}`);

    // Get the controllino sensor service to send the command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      controllinoSensor.sendCommand(command);
    } else {
      this.log.warn('Cannot send command - sensor service does not support sendCommand');
    }
  }

  private async waitForControllinoScores(players: any[]): Promise<ControllinoScoreResponse> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        if (this.scoreSubscription) {
          this.scoreSubscription.unsubscribe();
        }
        reject(new Error('Timeout waiting for controllino scores'));
      }, 300000); // 5 minute timeout

      this.log.log('Waiting for controllino score response...');

      // Listen for score response from controllino
      this.scoreSubscription = this.sensors.onEvent().subscribe((event) => {
        this.log.debug(`Received sensor event while waiting for scores: ${JSON.stringify(event)}`);

        // Check if this is a score response event
        if (this.isScoreResponse(event)) {
          this.log.log('Score response detected, parsing...');
          clearTimeout(timeout);
          if (this.scoreSubscription) {
            this.scoreSubscription.unsubscribe();
          }

          const scores = this.parseScoreResponse(event);
          this.log.log(`Parsed scores: ${JSON.stringify(scores)}`);
          resolve(scores);
        }
      });

      // For testing/simulation: auto-generate scores after 15 seconds
      if (this.cfg.get('global.mode') === 'SIM') {
        this.setupSimulationScoreGeneration(resolve, reject, timeout, players);
      }
    });
  }

  private setupSimulationScoreGeneration(
    resolve: (value: ControllinoScoreResponse) => void,
    _reject: (reason?: any) => void,
    timeout: any,
    players: any[]
  ): void {
    // In simulation mode, auto-generate scores after 15 seconds
    setTimeout(() => {
      this.log.log('Simulation mode: Auto-generating test scores after 30 seconds');

      if (this.scoreSubscription) {
        this.scoreSubscription.unsubscribe();
      }
      clearTimeout(timeout);

      // Get jackpot threshold from config
      const jackpotThreshold = this.cfg.get<number>('game.jackpotThreshold') || 1000;

      // Generate random scores for each player using their badge IDs
      const testScores: ControllinoScoreResponse = {};
      players.forEach(player => {
        // 20% chance of jackpot score
        const isJackpotRoll = Math.random() < 0.2;

        let score: number;
        if (isJackpotRoll) {
          // Jackpot: threshold + random 0-500
          score = jackpotThreshold + Math.floor(Math.random() * 500);
          this.log.log(`🎰 Jackpot generated for ${player.badgeId}: ${score} points!`);
        } else {
          // Normal score: 100 to (threshold - 1)
          const maxNormal = jackpotThreshold - 1;
          score = Math.floor(Math.random() * (maxNormal - 100 + 1)) + 100;
        }

        testScores[player.badgeId] = score;
      });

      this.log.log(`Generated test scores for ${players.length} players: ${JSON.stringify(testScores)}`);
      resolve(testScores);
    }, 30000); // 30 seconds for testing
  }

  private isScoreResponse(event: any): boolean {
    // Check if this is a score response from controllino
    // Format: SCORE:<BADGE1>:<POINTS1>,<BADGE2>:<POINTS2>,...

    if (typeof event === 'object' && event !== null) {
      return event.id === 'SCORE' ||
             (typeof event.data === 'string' && event.data.startsWith('SCORE:'));
    }
    return false;
  }

  private parseScoreResponse(event: any): ControllinoScoreResponse {
    // Parse the controllino score response
    // Format: SCORE:<BADGE1>:<POINTS1>,<BADGE2>:<POINTS2>,...

    try {
      if (typeof event.data === 'string' && event.data.startsWith('SCORE:')) {
        const scoreData = event.data.substring(6); // Remove "SCORE:" prefix
        const scores: ControllinoScoreResponse = {};

        const playerScores = scoreData.split(',');
        for (const playerScore of playerScores) {
          const [badgeId, points] = playerScore.split(':');
          if (badgeId && points) {
            scores[badgeId.trim()] = parseInt(points.trim()) || 0;
          }
        }

        this.log.log(`Parsed scores: ${JSON.stringify(scores)}`);
        return scores;
      }

    } catch (error) {
      this.log.error(`Error parsing score response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Fallback: return empty scores
    this.log.warn('Could not parse score response, returning empty scores');
    return {};
  }

  private async submitTeamScores(gameId: number, players: any[], scores: ControllinoScoreResponse): Promise<void> {
    try {
      // Get jackpot threshold from config
      const jackpotThreshold = this.cfg.get<number>('game.jackpotThreshold') || 1000;

      // Map badge-based scores to players
      const playerScores: PlayerScoreData[] = [];

      for (const player of players) {
        const badgeId = player.badgeId;
        const playerPoints = scores[badgeId];

        if (playerPoints !== undefined && playerPoints > 0) {
          const isJackpot = playerPoints >= jackpotThreshold;

          playerScores.push({
            playerId: player.id,
            playerPoints: playerPoints,
            isJackpot: isJackpot
          });

          if (isJackpot) {
            this.log.log(`🎰 JACKPOT! Player ${player.displayName} (${badgeId}): ${playerPoints} points`);
          } else {
            this.log.log(`Player ${player.displayName} (${badgeId}): ${playerPoints} points`);
          }
        } else {
          this.log.warn(`No score found for player ${player.displayName} (${badgeId})`);
        }
      }

      if (playerScores.length === 0) {
        this.log.warn('No valid scores to submit');
        return;
      }

      const scoreRequest: TeamScoreRequest = {
        gameId,
        players: playerScores
      };

      this.log.log(`Submitting scores for ${playerScores.length} players`);
      const response = await this.api.createTeamScore(scoreRequest);

      if (response.code === 200) {
        this.log.log('Team scores submitted successfully');
      } else {
        this.log.error(`Failed to submit scores: ${response.message}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Error submitting team scores: ${errorMessage}`);
    }
  }
}
