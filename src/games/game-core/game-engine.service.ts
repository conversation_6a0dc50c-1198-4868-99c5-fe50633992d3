import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  NFC_READER,
  SENSOR_BUS,
  DISPLAY,
  LED_CONTROL,
} from '../../hardware/tokens';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { SensorBusService } from '../../hardware/interfaces/sensor-bus.interface';
import { DisplayService } from '../../hardware/interfaces/display.interface';
import {
  LedControlService,
  LedColor,
} from '../../hardware/interfaces/led-control.interface';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, Subscription } from 'rxjs';
import { ApiService } from '../../api/api.service';
import { DisplayGateway } from 'src/ui/display.gateway';
import { ControllinoSensorService } from '../../hardware/sensors/controllino-sensor.service';
import { TeamScoreRequest, PlayerScoreData } from '../../common/dto/game‑manager.dto';

// Interface for controllino score response - now individual player scores
interface ControllinoScoreResponse {
  [playerOrder: string]: number; // Player order (1-4) -> score
}

@Injectable()
export class GameEngineService {
  private readonly log = new Logger(GameEngineService.name);
  private arcadeMachineOutput: string;
  private scoreSubscription?: Subscription;

  constructor(
    @Inject(NFC_READER) private nfc: NfcReaderService,
    @Inject(SENSOR_BUS) private sensors: SensorBusService,
    @Inject(DISPLAY) private display: DisplayService,
    @Inject(LED_CONTROL) private led: LedControlService,
    private cfg: ConfigService,
    private api: ApiService,
    private ui: DisplayGateway,
  ) {
    // Get arcade machine output mapping from config
    this.arcadeMachineOutput = this.cfg.get<string>(
      'global.hardware.controllino.outputs.arcadeMachine',
      '04'
    );

    this.log.log(`Arcade machine output mapping: ${this.arcadeMachineOutput}`);
    this.run();
  }

  private async run() {
    this.display.showSplash('Scan your team badge');
    // Set green LED while waiting for team
    this.led.setColor(LedColor.GREEN);
    const tag = await firstValueFrom(this.nfc.onTag());
    const gameId = this.cfg.get<number>('global.gameId') ?? 1;

    const adminBadges = this.cfg.get<string[]>('global.adminBadges') ?? [];
    const isAdmin = adminBadges.includes(tag);

    let teamData: any;
    let players: any[] = [];

    if (isAdmin) {
      this.log.warn(`Admin badge '${tag}' detected – bypassing API`);
      teamData = { id: 0, name: 'Admin Team' };
      players = [
        { id: 1, displayName: 'Admin Player 1' },
        { id: 2, displayName: 'Admin Player 2' },
        { id: 3, displayName: 'Admin Player 3' },
        { id: 4, displayName: 'Admin Player 4' }
      ];
    } else {
      const auth = await this.api.authorizeTeam(tag, gameId).catch(() => {});
      if (!auth || auth.code !== 200 || !auth.team || !auth.players) {
        this.display.showResult('🚫 Team Unauthorized');
        return this.run();
      }
      teamData = auth.team;
      players = auth.players;
    }

    this.log.log(`Team authorized: ${teamData.name} with ${players.length} players`);

    // Start the arcade game
    await this.startArcadeGame(teamData, players, isAdmin, gameId);
  }

  private async startArcadeGame(teamData: any, players: any[], isAdmin: boolean, gameId: number) {
    // Broadcast start message to UI
    const startMsg = {
      action: 'start',
      gameName: 'Arcade Game',
      instructions: 'Game starting - controllino will handle the gameplay',
      playerDisplayName: teamData.name,
      timer: 0, // No timer - controllino handles timing
    } as const;

    this.log.debug(`Broadcasting start message: ${JSON.stringify(startMsg)}`);
    this.ui.broadcast(startMsg);

    // Set yellow LED and send team setup to controllino
    this.led.setColor(LedColor.YELLOW);
    this.sendTeamSetupCommand(players);
    this.sendArcadeMachineCommand(true);
    this.display.showSplash('Game in progress...');

    try {
      // Wait for controllino to send score response
      const scores = await this.waitForControllinoScores(players);

      // Turn off arcade machine and set blue LED
      this.sendArcadeMachineCommand(false);
      this.led.setColor(LedColor.BLUE);

      this.log.log(`Game finished - scores received: ${JSON.stringify(scores)}`);

      // Submit team scores to API (if not admin)
      if (!isAdmin) {
        await this.submitTeamScores(gameId, players, scores);
      }

      // Send game completion to WebSocket clients
      const websocketDisplay = this.display as any;
      if (websocketDisplay.sendGameComplete) {
        const scoreArray = Object.entries(scores).map(([playerOrder, points]) => ({
          playerOrder: parseInt(playerOrder),
          points,
          player: players[parseInt(playerOrder) - 1] // Convert 1-based to 0-based index
        }));
        websocketDisplay.sendGameComplete(scoreArray);
      } else {
        this.display.showResult(`Game Complete! Scores: ${JSON.stringify(scores)}`);
      }

      // Show success LED for 3 seconds
      await firstValueFrom(this.led.setColorForDuration(LedColor.GREEN, 3000));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Game error: ${errorMessage}`);
      this.sendArcadeMachineCommand(false); // Ensure machine is turned off
      this.led.setColor(LedColor.RED);
      this.display.showResult('Game Error - Please try again');

      // Show error LED for 3 seconds
      await firstValueFrom(this.led.setColorForDuration(LedColor.RED, 3000));
    }

    // Wait before restarting
    await new Promise((resolve) => setTimeout(resolve, 3000));
    return this.run(); // restart
  }

  private sendTeamSetupCommand(players: any[]): void {
    // Create binary team setup command: A01 + 4 UIDs (4 bytes each)
    const playerCount = players.length;
    this.log.log(`Sending binary team setup command for ${playerCount} players`);

    // Create 19-byte buffer: A01 + 16 bytes for UIDs
    const buf = Buffer.alloc(19);

    // Command header: A01
    buf[0] = 0x41; // 'A'
    buf[1] = 0x30; // '0'
    buf[2] = 0x31; // '1'

    // Fill player UIDs (convert hex badge IDs to bytes)
    for (let i = 0; i < 4; i++) {
      const startIndex = 3 + (i * 4);

      if (i < players.length && players[i].badgeId) {
        // Convert hex badge ID to 4 bytes
        const badgeId = players[i].badgeId.toUpperCase().padStart(8, '0');
        this.log.log(`Player ${i + 1}: ${players[i].displayName} (${badgeId})`);

        try {
          for (let j = 0; j < 4; j++) {
            const hexByte = badgeId.substr(j * 2, 2);
            buf[startIndex + j] = parseInt(hexByte, 16);
          }
        } catch (error) {
          this.log.error(`Error parsing badge ID ${badgeId}: ${error}`);
          // Fill with zeros on error
          buf.fill(0x00, startIndex, startIndex + 4);
        }
      } else {
        // Pad with zeros for missing players
        buf.fill(0x00, startIndex, startIndex + 4);
        this.log.log(`Player ${i + 1}: [EMPTY - padded with 0x00]`);
      }
    }

    this.log.log(`Binary command: ${buf.toString('hex').toUpperCase()}`);

    // Send binary command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendBinaryCommand) {
      controllinoSensor.sendBinaryCommand(buf);
    } else {
      this.log.warn('Cannot send binary team setup command - sensor service does not support sendBinaryCommand');
    }
  }

  private sendArcadeMachineCommand(turnOn: boolean): void {
    const command = `O${this.arcadeMachineOutput}${turnOn ? '1' : '0'}`;
    this.log.log(`Sending arcade machine command: ${command}`);

    // Get the controllino sensor service to send the command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      controllinoSensor.sendCommand(command);
    } else {
      this.log.warn('Cannot send command - sensor service does not support sendCommand');
    }
  }

  private async waitForControllinoScores(players: any[]): Promise<ControllinoScoreResponse> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        if (this.scoreSubscription) {
          this.scoreSubscription.unsubscribe();
        }
        reject(new Error('Timeout waiting for controllino scores'));
      }, 300000); // 5 minute timeout

      this.log.log(`Waiting for individual score events from ${players.length} players...`);

      const receivedScores: ControllinoScoreResponse = {};
      let expectedPlayerCount = players.length;

      // Listen to arduino.js compatible EventInput events
      const controllinoSensor = this.sensors as any;
      if (controllinoSensor.emitter) {
        const eventHandler = (numEvent: string, input: number) => {
          this.log.log(`Received score event: Player ${numEvent}, Score: ${input}`);

          // numEvent should be '1', '2', '3', or '4' (player order)
          if (['1', '2', '3', '4'].includes(numEvent)) {
            receivedScores[numEvent] = input;

            // Check if we have all expected scores
            const receivedCount = Object.keys(receivedScores).length;
            this.log.log(`Received ${receivedCount}/${expectedPlayerCount} player scores`);

            if (receivedCount >= expectedPlayerCount) {
              // All scores received
              controllinoSensor.emitter.off('EventInput', eventHandler);
              clearTimeout(timeout);

              this.log.log(`All scores received: ${JSON.stringify(receivedScores)}`);
              resolve(receivedScores);
            }
          }
        };

        controllinoSensor.emitter.on('EventInput', eventHandler);
      } else {
        this.log.warn('EventEmitter not available on controllino sensor service');
        reject(new Error('EventEmitter not available'));
      }

      // For testing/simulation: auto-generate scores after 15 seconds
      if (this.cfg.get('global.mode') === 'SIM') {
        this.setupSimulationScoreGeneration(resolve, reject, timeout, players);
      }
    });
  }

  private setupSimulationScoreGeneration(
    resolve: (value: ControllinoScoreResponse) => void,
    _reject: (reason?: any) => void,
    timeout: any,
    players: any[]
  ): void {
    // In simulation mode, auto-generate scores after 15 seconds
    setTimeout(() => {
      this.log.log('Simulation mode: Auto-generating test scores after 15 seconds');

      if (this.scoreSubscription) {
        this.scoreSubscription.unsubscribe();
      }
      clearTimeout(timeout);

      // Get jackpot threshold from config
      const jackpotThreshold = this.cfg.get<number>('game.jackpotThreshold') || 1000;

      // Generate individual player score events (1-4)
      const testScores: ControllinoScoreResponse = {};

      // Simulate individual EventInput events for each player
      const controllinoSensor = this.sensors as any;
      if (controllinoSensor.emitter) {
        players.forEach((player, index) => {
          const playerOrder = (index + 1).toString(); // 1, 2, 3, 4

          // 20% chance of jackpot score
          const isJackpotRoll = Math.random() < 0.2;

          let score: number;
          if (isJackpotRoll) {
            // Jackpot: threshold + random 0-500
            score = jackpotThreshold + Math.floor(Math.random() * 500);
            this.log.log(`🎰 Jackpot generated for Player ${playerOrder} (${player.displayName}): ${score} points!`);
          } else {
            // Normal score: 100 to (threshold - 1)
            const maxNormal = jackpotThreshold - 1;
            score = Math.floor(Math.random() * (maxNormal - 100 + 1)) + 100;
          }

          testScores[playerOrder] = score;

          // Emit individual EventInput event with slight delay
          setTimeout(() => {
            controllinoSensor.emitter.emit('EventInput', playerOrder, score);
            this.log.log(`Simulated EventInput: Player ${playerOrder}, Score: ${score}`);
          }, index * 100); // 100ms delay between each player
        });

        this.log.log(`Generated individual score events for ${players.length} players`);
      } else {
        // Fallback: resolve with scores directly
        this.log.warn('EventEmitter not available, using direct resolution');
        resolve(testScores);
      }
    }, 15000); // 15 seconds as requested
  }

  // These methods are no longer needed - we use individual EventInput events

  private async submitTeamScores(gameId: number, players: any[], scores: ControllinoScoreResponse): Promise<void> {
    try {
      // Get jackpot threshold from config
      const jackpotThreshold = this.cfg.get<number>('game.jackpotThreshold') || 1000;

      // Map player order scores to actual players
      const playerScores: PlayerScoreData[] = [];

      for (let i = 0; i < players.length; i++) {
        const player = players[i];
        const playerOrder = (i + 1).toString(); // 1, 2, 3, 4
        const playerPoints = scores[playerOrder];

        if (playerPoints !== undefined && playerPoints > 0) {
          const isJackpot = playerPoints >= jackpotThreshold;

          playerScores.push({
            playerId: player.id,
            playerPoints: playerPoints,
            isJackpot: isJackpot
          });

          if (isJackpot) {
            this.log.log(`🎰 JACKPOT! Player ${i + 1} (${player.displayName}): ${playerPoints} points`);
          } else {
            this.log.log(`Player ${i + 1} (${player.displayName}): ${playerPoints} points`);
          }
        } else {
          this.log.warn(`No score found for Player ${i + 1} (${player.displayName})`);
        }
      }

      if (playerScores.length === 0) {
        this.log.warn('No valid scores to submit');
        return;
      }

      const scoreRequest: TeamScoreRequest = {
        gameId,
        players: playerScores
      };

      this.log.log(`Submitting scores for ${playerScores.length} players`);
      const response = await this.api.createTeamScore(scoreRequest);

      if (response.code === 200) {
        this.log.log('Team scores submitted successfully');
      } else {
        this.log.error(`Failed to submit scores: ${response.message}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Error submitting team scores: ${errorMessage}`);
    }
  }
}
