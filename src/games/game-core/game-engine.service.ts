import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  NFC_READER,
  SENSOR_BUS,
  DISPLAY,
  LED_CONTROL,
} from '../../hardware/tokens';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { SensorBusService } from '../../hardware/interfaces/sensor-bus.interface';
import { DisplayService } from '../../hardware/interfaces/display.interface';
import {
  LedControlService,
} from '../../hardware/interfaces/led-control.interface';
import { ConfigService } from '@nestjs/config';
import { Subscription } from 'rxjs';
import { ApiService } from '../../api/api.service';
import { DisplayGateway } from 'src/ui/display.gateway';
import { ControllinoSensorService } from '../../hardware/sensors/controllino-sensor.service';
import { TeamScoreRequest, PlayerScoreData } from '../../common/dto/game‑manager.dto';

// Interface for controllino score response - now individual player scores
interface ControllinoScoreResponse {
  [playerOrder: string]: number; // Player order (1-4) -> score
}

@Injectable()
export class GameEngineService {
  private readonly log = new Logger(GameEngineService.name);
  private arcadeMachineOutput: string;
  private scoreSubscription?: Subscription;
  private gameInProgress = false;

  constructor(
    @Inject(NFC_READER) private nfc: NfcReaderService,
    @Inject(SENSOR_BUS) private sensors: SensorBusService,
    @Inject(DISPLAY) private display: DisplayService,
    @Inject(LED_CONTROL) private led: LedControlService,
    private cfg: ConfigService,
    private api: ApiService,
    private ui: DisplayGateway,
  ) {
    // Get arcade machine output mapping from config
    this.arcadeMachineOutput = this.cfg.get<string>(
      'hardware.controllino.outputs.arcadeMachine',
      '04'
    );

    this.log.log(`Arcade machine output mapping: ${this.arcadeMachineOutput}`);
    this.initializeEventListeners();
    this.setAvailableState();
  }

  private initializeEventListeners() {
    // Listen for badge scans continuously
    this.nfc.onTag().subscribe((tag: string) => {
      this.handleBadgeScan(tag);
    });
  }

  private setAvailableState() {
    this.gameInProgress = false;
    this.display.showSplash('Scan your team badge');
    this.setGreenIndicator();
    console.log('🟢 Cell is available - ready for badge scan');
  }

  private async handleBadgeScan(tag: string) {
    // Guard: Check if cell is occupied
    if (this.gameInProgress) {
      console.log('🚫 Cell is currently in use - please wait');
      return; // Ignore badge scan
    }

    const gameId = this.cfg.get<number>('global.gameId') ?? 1;

    const adminBadges = this.cfg.get<string[]>('global.adminBadges') ?? [];
    const isAdmin = adminBadges.includes(tag);

    let teamData: any;
    let players: any[] = [];

    if (isAdmin) {
      this.log.warn(`Admin badge '${tag}' detected – bypassing API`);
      teamData = { id: 0, name: 'Admin Team' };
      players = [
        { id: 1, displayName: 'Admin Player 1' },
        { id: 2, displayName: 'Admin Player 2' },
        { id: 3, displayName: 'Admin Player 3' },
        { id: 4, displayName: 'Admin Player 4' }
      ];
    } else {
      const auth = await this.api.authorizeTeam(tag, gameId).catch(() => {});
      if (!auth || auth.code !== 200 || !auth.team || !auth.players) {
        console.log('❌ Authorization failed - please try again');
        return; // Return to main loop
      }
      teamData = auth.team;
      players = auth.players;
    }

    // Clean display of team info
    console.log(`✅ Team authorized: ${teamData.name}`);
    console.log(`👥 Players: ${players.map(p => p.displayName).join(', ')}`);

    // Set red LED (occupied) and turn on cell light
    this.setRedIndicator();
    this.powerOnCell();

    // Start the arcade game
    await this.startArcadeGame(teamData, players, isAdmin, gameId);
  }

  private async startArcadeGame(teamData: any, players: any[], isAdmin: boolean, gameId: number) {
    this.gameInProgress = true; // Mark cell as occupied

    try {
      console.log('🎮 Starting game...');

      // Broadcast start message to UI
      const startMsg = {
        action: 'start',
        gameName: 'Arcade Game',
        instructions: 'Game starting - controllino will handle the gameplay',
        playerDisplayName: teamData.name,
        timer: 0, // No timer - controllino handles timing
      } as const;

      this.log.debug(`Broadcasting start message: ${JSON.stringify(startMsg)}`);
      this.ui.broadcast(startMsg);

      // Send team setup and start arcade machine
      // Red LED already set when team was authorized (occupied state)
      this.sendTeamSetupCommand(players);
      this.sendArcadeMachineCommand(true);
      this.display.showSplash('Game in progress...');

      // Wait for controllino to send score response
      const scores = await this.waitForControllinoScores(players);

      // Turn off arcade machine and set green LED (available again)
      this.sendArcadeMachineCommand(false);
      this.setGreenIndicator();

      console.log('🏁 Game Complete!');

      // Display clean score results
      Object.entries(scores).forEach(([playerOrder, points]) => {
        const player = players[parseInt(playerOrder) - 1];
        const isJackpot = points >= (this.cfg.get<number>('game.jackpotThreshold') || 1000);
        if (isJackpot) {
          console.log(`🎰 JACKPOT! ${player.displayName}: ${points} points`);
        } else {
          console.log(`⭐ ${player.displayName}: ${points} points`);
        }
      });

      // Submit team scores to API (if not admin)
      if (!isAdmin) {
        await this.submitTeamScores(gameId, players, scores);
        console.log('✅ Scores submitted successfully');
      }

      // Send game completion to WebSocket clients
      const websocketDisplay = this.display as any;
      if (websocketDisplay.sendGameComplete) {
        const scoreArray = Object.entries(scores).map(([playerOrder, points]) => ({
          playerOrder: parseInt(playerOrder),
          points,
          player: players[parseInt(playerOrder) - 1] // Convert 1-based to 0-based index
        }));
        websocketDisplay.sendGameComplete(scoreArray);
      }

      // Show success state for 3 seconds, then return to available
      await new Promise((resolve) => setTimeout(resolve, 3000));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Game error: ${errorMessage}`);
      this.sendArcadeMachineCommand(false); // Ensure machine is turned off
      this.setRedIndicator(); // Keep red (error state)
      this.display.showResult('Game Error - Please try again');

      // Wait 3 seconds then return to available state
      await new Promise((resolve) => setTimeout(resolve, 3000));
    } finally {
      // Always reset game state when game ends
      this.setAvailableState();
    }
  }

  private sendTeamSetupCommand(players: any[]): void {
    // Create binary team setup command: A01 + 4 UIDs (4 bytes each)
    const playerCount = players.length;
    this.log.log(`Sending binary team setup command for ${playerCount} players`);

    // Create 19-byte buffer: A01 + 16 bytes for UIDs
    const buf = Buffer.alloc(19);

    // Command header: A01
    buf[0] = 0x41; // 'A'
    buf[1] = 0x30; // '0'
    buf[2] = 0x31; // '1'

    // Fill player UIDs (convert hex badge IDs to bytes)
    for (let i = 0; i < 4; i++) {
      const startIndex = 3 + (i * 4);

      if (i < players.length && players[i].badgeId) {
        // Convert hex badge ID to 4 bytes
        const badgeId = players[i].badgeId.toUpperCase().padStart(8, '0');
        this.log.log(`Player ${i + 1}: ${players[i].displayName} (${badgeId})`);

        try {
          for (let j = 0; j < 4; j++) {
            const hexByte = badgeId.substr(j * 2, 2);
            buf[startIndex + j] = parseInt(hexByte, 16);
          }
        } catch (error) {
          this.log.error(`Error parsing badge ID ${badgeId}: ${error}`);
          // Fill with zeros on error
          buf.fill(0x00, startIndex, startIndex + 4);
        }
      } else {
        // Pad with zeros for missing players
        buf.fill(0x00, startIndex, startIndex + 4);
        this.log.log(`Player ${i + 1}: [EMPTY - padded with 0x00]`);
      }
    }

    this.log.log(`Binary command: ${buf.toString('hex').toUpperCase()}`);

    // Send binary command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendBinaryCommand) {
      controllinoSensor.sendBinaryCommand(buf);
    } else {
      this.log.warn('Cannot send binary team setup command - sensor service does not support sendBinaryCommand');
    }
  }

  private sendArcadeMachineCommand(turnOn: boolean): void {
    const stateOn = this.cfg.get<string>('hardware.controllino.states.on') || '1';
    const stateOff = this.cfg.get<string>('hardware.controllino.states.off') || '0';
    const command = `O${this.arcadeMachineOutput}+${turnOn ? stateOn : stateOff}`;
    this.log.log(`Sending arcade machine command: ${command}`);

    // Get the controllino sensor service to send the command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      controllinoSensor.sendCommand(command);
    } else {
      this.log.warn('Cannot send command - sensor service does not support sendCommand');
    }
  }

  private async waitForControllinoScores(players: any[]): Promise<ControllinoScoreResponse> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        if (this.scoreSubscription) {
          this.scoreSubscription.unsubscribe();
        }
        reject(new Error('Timeout waiting for controllino scores'));
      }, 300000); // 5 minute timeout

      this.log.log(`Waiting for individual score events from ${players.length} players...`);

      const receivedScores: ControllinoScoreResponse = {};
      let expectedPlayerCount = players.length;
      let simulationTimeoutId: NodeJS.Timeout | null = null;

      // Listen to arduino.js compatible EventInput events
      const controllinoSensor = this.sensors as any;
      if (controllinoSensor.emitter) {
        const eventHandler = (numEvent: string, input: number) => {
          this.log.log(`Received score event: Player ${numEvent}, Score: ${input}`);

          // numEvent should be '1', '2', '3', or '4' (player order)
          if (['1', '2', '3', '4'].includes(numEvent)) {
            receivedScores[numEvent] = input;

            // Check if we have all expected scores
            const receivedCount = Object.keys(receivedScores).length;
            this.log.log(`Received ${receivedCount}/${expectedPlayerCount} player scores`);

            if (receivedCount >= expectedPlayerCount) {
              // All scores received - clear all timeouts
              controllinoSensor.emitter.off('EventInput', eventHandler);
              clearTimeout(timeout);
              if (simulationTimeoutId) {
                clearTimeout(simulationTimeoutId);
                simulationTimeoutId = null;
              }

              this.log.log(`All scores received: ${JSON.stringify(receivedScores)}`);
              resolve(receivedScores);
            }
          }
        };

        controllinoSensor.emitter.on('EventInput', eventHandler);
      } else {
        this.log.warn('EventEmitter not available on controllino sensor service');
        reject(new Error('EventEmitter not available'));
      }

      // For testing/simulation: auto-generate scores after 15 seconds
      if (this.cfg.get('global.mode') === 'SIM') {
        simulationTimeoutId = this.setupSimulationScoreGeneration(resolve, reject, timeout, players);
      }
    });
  }

  private setupSimulationScoreGeneration(
    resolve: (value: ControllinoScoreResponse) => void,
    _reject: (reason?: any) => void,
    timeout: any,
    players: any[]
  ): NodeJS.Timeout {
    // In simulation mode, auto-generate scores after 15 seconds
    return setTimeout(() => {
      this.log.log('Simulation mode: Auto-generating test scores after 15 seconds');

      if (this.scoreSubscription) {
        this.scoreSubscription.unsubscribe();
      }
      clearTimeout(timeout);

      // Get jackpot threshold from config
      const jackpotThreshold = this.cfg.get<number>('game.jackpotThreshold') || 1000;

      // Generate individual player score events (1-4)
      const testScores: ControllinoScoreResponse = {};

      // Simulate individual EventInput events for each player
      const controllinoSensor = this.sensors as any;
      if (controllinoSensor.emitter) {
        players.forEach((player, index) => {
          const playerOrder = (index + 1).toString(); // 1, 2, 3, 4

          // 20% chance of jackpot score
          const isJackpotRoll = Math.random() < 0.2;

          let score: number;
          if (isJackpotRoll) {
            // Jackpot: threshold + random 0-500
            score = jackpotThreshold + Math.floor(Math.random() * 500);
            this.log.log(`🎰 Jackpot generated for Player ${playerOrder} (${player.displayName}): ${score} points!`);
          } else {
            // Normal score: 100 to (threshold - 1)
            const maxNormal = jackpotThreshold - 1;
            score = Math.floor(Math.random() * (maxNormal - 100 + 1)) + 100;
          }

          testScores[playerOrder] = score;

          // Emit individual EventInput event with slight delay
          setTimeout(() => {
            controllinoSensor.emitter.emit('EventInput', playerOrder, score);
            this.log.log(`Simulated EventInput: Player ${playerOrder}, Score: ${score}`);
          }, index * 100); // 100ms delay between each player
        });

        this.log.log(`Generated individual score events for ${players.length} players`);
      } else {
        // Fallback: resolve with scores directly
        this.log.warn('EventEmitter not available, using direct resolution');
        resolve(testScores);
      }
    }, 30000); // 30 seconds for testing
  }



  private async submitTeamScores(gameId: number, players: any[], scores: ControllinoScoreResponse): Promise<void> {
    try {
      // Get jackpot threshold from config
      const jackpotThreshold = this.cfg.get<number>('game.jackpotThreshold') || 1000;

      // Map player order scores to actual players
      const playerScores: PlayerScoreData[] = [];

      for (let i = 0; i < players.length; i++) {
        const player = players[i];
        const playerOrder = (i + 1).toString(); // 1, 2, 3, 4
        const playerPoints = scores[playerOrder];

        if (playerPoints !== undefined && playerPoints > 0) {
          const isJackpot = playerPoints >= jackpotThreshold;

          playerScores.push({
            playerId: player.id,
            playerPoints: playerPoints,
            isJackpot: isJackpot
          });

          if (isJackpot) {
            this.log.log(`🎰 JACKPOT! Player ${i + 1} (${player.displayName}): ${playerPoints} points`);
          } else {
            this.log.log(`Player ${i + 1} (${player.displayName}): ${playerPoints} points`);
          }
        } else {
          this.log.warn(`No score found for Player ${i + 1} (${player.displayName})`);
        }
      }

      if (playerScores.length === 0) {
        this.log.warn('No valid scores to submit');
        return;
      }

      const scoreRequest: TeamScoreRequest = {
        gameId,
        players: playerScores
      };

      this.log.log(`Submitting scores for ${playerScores.length} players`);
      const response = await this.api.createTeamScore(scoreRequest);

      if (response.code === 200) {
        this.log.log('Team scores submitted successfully');
      } else {
        this.log.error(`Failed to submit scores: ${response.message}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Error submitting team scores: ${errorMessage}`);
    }
  }

  // Arduino.js compatible LED and door control methods
  private setGreenIndicator(): void {
    // Green LED ON = Available/Ready (like arduino.js turnOnGreenIndicator)
    this.log.log('Setting GREEN indicator (available)');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const greenLed = this.cfg.get<string>('hardware.controllino.outputs.greenLed') || '02';
      const redLed = this.cfg.get<string>('hardware.controllino.outputs.redLed') || '01';
      const stateOn = this.cfg.get<string>('hardware.controllino.states.on') || '1';
      const stateOff = this.cfg.get<string>('hardware.controllino.states.off') || '0';

      controllinoSensor.sendCommand(`O${greenLed}+${stateOn}`); // Green LED ON
      controllinoSensor.sendCommand(`O${redLed}+${stateOff}`);  // Red LED OFF
    }
  }

  private setRedIndicator(): void {
    // Red LED ON = Occupied/In Use (like arduino.js turnOffGreenIndicator)
    this.log.log('Setting RED indicator (occupied)');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const greenLed = this.cfg.get<string>('hardware.controllino.outputs.greenLed') || '02';
      const redLed = this.cfg.get<string>('hardware.controllino.outputs.redLed') || '01';
      const stateOn = this.cfg.get<string>('hardware.controllino.states.on') || '1';
      const stateOff = this.cfg.get<string>('hardware.controllino.states.off') || '0';

      controllinoSensor.sendCommand(`O${redLed}+${stateOn}`);   // Red LED ON
      controllinoSensor.sendCommand(`O${greenLed}+${stateOff}`); // Green LED OFF
    }
  }

  private powerOnCell(): void {
    // Turn on cell light (like arduino.js powerOnCell)
    this.log.log('Turning ON cell light');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const cellLight = this.cfg.get<string>('hardware.controllino.outputs.cellLight') || '99';
      const stateOn = this.cfg.get<string>('hardware.controllino.states.on') || '1';

      controllinoSensor.sendCommand(`O${cellLight}+${stateOn}`); // Cell light ON
    }
  }

  private powerOffCell(): void {
    // Turn off cell light (like arduino.js powerOffCell)
    this.log.log('Turning OFF cell light');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const cellLight = this.cfg.get<string>('hardware.controllino.outputs.cellLight') || '99';
      const stateOff = this.cfg.get<string>('hardware.controllino.states.off') || '0';

      controllinoSensor.sendCommand(`O${cellLight}+${stateOff}`); // Cell light OFF
    }
  }

  private openDoor(): void {
    // Open door temporarily (like arduino.js openDoor)
    // Note: Door logic is reversed - OUT_OFF opens, OUT_ON closes
    this.log.log('Opening door temporarily');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const door = this.cfg.get<string>('hardware.controllino.outputs.door') || '05';
      const stateOn = this.cfg.get<string>('hardware.controllino.states.on') || '1';
      const stateOff = this.cfg.get<string>('hardware.controllino.states.off') || '0';

      controllinoSensor.sendCommand(`O${door}+${stateOff}`); // Door OPEN (reversed logic)

      // Close door after 10 seconds (like arduino.js DURATION_DOOR)
      setTimeout(() => {
        controllinoSensor.sendCommand(`O${door}+${stateOn}`); // Door CLOSE (reversed logic)
        this.log.log('Door closed after timeout');
      }, 10000);
    }
  }
}
