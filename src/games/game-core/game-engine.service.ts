import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  NFC_READER,
  SENSOR_BUS,
  DISPLAY,
  LED_CONTROL,
} from '../../hardware/tokens';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { SensorBusService } from '../../hardware/interfaces/sensor-bus.interface';
import { DisplayService } from '../../hardware/interfaces/display.interface';
import {
  LedControlService,
  LedColor,
} from '../../hardware/interfaces/led-control.interface';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, Subscription } from 'rxjs';
import { ApiService } from '../../api/api.service';
import { DisplayGateway } from 'src/ui/display.gateway';
import { ControllinoSensorService } from '../../hardware/sensors/controllino-sensor.service';
import { TeamScoreRequest, PlayerScoreData } from '../../common/dto/game‑manager.dto';

// Interface for controllino score response
interface ControllinoScoreResponse {
  player1: number;
  player2: number;
  player3: number;
  player4: number;
}

@Injectable()
export class GameEngineService {
  private readonly log = new Logger(GameEngineService.name);
  private arcadeMachineOutput: string;
  private scoreSubscription?: Subscription;

  constructor(
    @Inject(NFC_READER) private nfc: NfcReaderService,
    @Inject(SENSOR_BUS) private sensors: SensorBusService,
    @Inject(DISPLAY) private display: DisplayService,
    @Inject(LED_CONTROL) private led: LedControlService,
    private cfg: ConfigService,
    private api: ApiService,
    private ui: DisplayGateway,
  ) {
    // Get arcade machine output mapping from config
    this.arcadeMachineOutput = this.cfg.get<string>(
      'global.hardware.controllino.outputs.arcadeMachine',
      '04'
    );

    this.log.log(`Arcade machine output mapping: ${this.arcadeMachineOutput}`);
    this.run();
  }

  private async run() {
    this.display.showSplash('Scan your team badge');
    // Set green LED while waiting for team
    this.led.setColor(LedColor.GREEN);
    const tag = await firstValueFrom(this.nfc.onTag());
    const gameId = this.cfg.get<number>('global.gameId') ?? 1;

    const adminBadges = this.cfg.get<string[]>('global.adminBadges') ?? [];
    const isAdmin = adminBadges.includes(tag);

    let teamData: any;
    let players: any[] = [];

    if (isAdmin) {
      this.log.warn(`Admin badge '${tag}' detected – bypassing API`);
      teamData = { id: 0, name: 'Admin Team' };
      players = [
        { id: 1, displayName: 'Admin Player 1' },
        { id: 2, displayName: 'Admin Player 2' },
        { id: 3, displayName: 'Admin Player 3' },
        { id: 4, displayName: 'Admin Player 4' }
      ];
    } else {
      const auth = await this.api.authorizeTeam(tag, gameId).catch(() => {});
      if (!auth || auth.code !== 200 || !auth.team || !auth.players) {
        this.display.showResult('🚫 Team Unauthorized');
        return this.run();
      }
      teamData = auth.team;
      players = auth.players;
    }

    this.log.log(`Team authorized: ${teamData.name} with ${players.length} players`);

    // Start the arcade game
    await this.startArcadeGame(teamData, players, isAdmin, gameId);
  }

  private async startArcadeGame(teamData: any, players: any[], isAdmin: boolean, gameId: number) {
    // Broadcast start message to UI
    const startMsg = {
      action: 'start',
      gameName: 'Arcade Game',
      instructions: 'Game starting - controllino will handle the gameplay',
      playerDisplayName: teamData.name,
      timer: 0, // No timer - controllino handles timing
    } as const;

    this.log.debug(`Broadcasting start message: ${JSON.stringify(startMsg)}`);
    this.ui.broadcast(startMsg);

    // Set yellow LED and turn on arcade machine
    this.led.setColor(LedColor.YELLOW);
    this.sendArcadeMachineCommand(true);
    this.display.showSplash('Game in progress...');

    try {
      // Wait for controllino to send score response
      const scores = await this.waitForControllinoScores();

      // Turn off arcade machine and set blue LED
      this.sendArcadeMachineCommand(false);
      this.led.setColor(LedColor.BLUE);

      this.log.log(`Game finished - scores received: ${JSON.stringify(scores)}`);
      this.display.showResult(`Game Complete! Scores: ${JSON.stringify(scores)}`);

      // Submit team scores to API (if not admin)
      if (!isAdmin) {
        await this.submitTeamScores(gameId, players, scores);
      }

      // Show success LED for 3 seconds
      await firstValueFrom(this.led.setColorForDuration(LedColor.GREEN, 3000));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Game error: ${errorMessage}`);
      this.sendArcadeMachineCommand(false); // Ensure machine is turned off
      this.led.setColor(LedColor.RED);
      this.display.showResult('Game Error - Please try again');

      // Show error LED for 3 seconds
      await firstValueFrom(this.led.setColorForDuration(LedColor.RED, 3000));
    }

    // Wait before restarting
    await new Promise((resolve) => setTimeout(resolve, 3000));
    return this.run(); // restart
  }

  private sendArcadeMachineCommand(turnOn: boolean): void {
    const command = `O${this.arcadeMachineOutput}${turnOn ? '1' : '0'}`;
    this.log.log(`Sending arcade machine command: ${command}`);

    // Get the controllino sensor service to send the command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      controllinoSensor.sendCommand(command);
    } else {
      this.log.warn('Cannot send command - sensor service does not support sendCommand');
    }
  }

  private async waitForControllinoScores(): Promise<ControllinoScoreResponse> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        if (this.scoreSubscription) {
          this.scoreSubscription.unsubscribe();
        }
        reject(new Error('Timeout waiting for controllino scores'));
      }, 300000); // 5 minute timeout

      this.log.log('Waiting for controllino score response...');

      // Listen for score response from controllino
      this.scoreSubscription = this.sensors.onEvent().subscribe((event) => {
        this.log.debug(`Received sensor event while waiting for scores: ${JSON.stringify(event)}`);

        // Check if this is a score response event
        if (this.isScoreResponse(event)) {
          this.log.log('Score response detected, parsing...');
          clearTimeout(timeout);
          if (this.scoreSubscription) {
            this.scoreSubscription.unsubscribe();
          }

          const scores = this.parseScoreResponse(event);
          this.log.log(`Parsed scores: ${JSON.stringify(scores)}`);
          resolve(scores);
        }
      });

      // For testing/simulation: also listen for manual score input
      // In simulation mode, you can trigger scores manually
      if (this.cfg.get('global.mode') === 'SIM') {
        this.setupManualScoreInput(resolve, reject, timeout);
      }
    });
  }

  private setupManualScoreInput(resolve: Function, reject: Function, timeout: NodeJS.Timeout): void {
    // In simulation mode, allow manual score input after 10 seconds
    setTimeout(() => {
      this.log.log('Simulation mode: Auto-generating test scores after 10 seconds');

      if (this.scoreSubscription) {
        this.scoreSubscription.unsubscribe();
      }
      clearTimeout(timeout);

      // Generate random test scores
      const testScores: ControllinoScoreResponse = {
        player1: Math.floor(Math.random() * 500) + 100,
        player2: Math.floor(Math.random() * 500) + 100,
        player3: Math.floor(Math.random() * 500) + 100,
        player4: Math.floor(Math.random() * 500) + 100
      };

      this.log.log(`Generated test scores: ${JSON.stringify(testScores)}`);
      resolve(testScores);
    }, 10000); // 10 seconds for testing
  }

  private isScoreResponse(event: any): boolean {
    // Check if this is a score response from controllino
    // Score responses should have a specific format or identifier
    // Example: event.id could be 'SCORE' or event could contain score data

    // For now, we'll check if the event has score-like properties
    // You'll need to adjust this based on your actual controllino protocol
    if (typeof event === 'object' && event !== null) {
      // Check if event contains score data (adjust based on your format)
      return event.id === 'SCORE' ||
             (event.hasOwnProperty('player1') && event.hasOwnProperty('player2')) ||
             (typeof event.data === 'string' && event.data.startsWith('SCORE:'));
    }
    return false;
  }

  private parseScoreResponse(event: any): ControllinoScoreResponse {
    // Parse the controllino score response based on your protocol
    // This implementation handles several possible formats:

    try {
      // Format 1: Direct object with player scores
      if (event.player1 !== undefined) {
        return {
          player1: parseInt(event.player1) || 0,
          player2: parseInt(event.player2) || 0,
          player3: parseInt(event.player3) || 0,
          player4: parseInt(event.player4) || 0
        };
      }

      // Format 2: String data like "SCORE:100,150,200,75"
      if (typeof event.data === 'string' && event.data.startsWith('SCORE:')) {
        const scoreData = event.data.substring(6); // Remove "SCORE:" prefix
        const scores = scoreData.split(',').map(s => parseInt(s.trim()) || 0);
        return {
          player1: scores[0] || 0,
          player2: scores[1] || 0,
          player3: scores[2] || 0,
          player4: scores[3] || 0
        };
      }

      // Format 3: JSON string in data field
      if (typeof event.data === 'string') {
        const parsed = JSON.parse(event.data);
        if (parsed.scores && Array.isArray(parsed.scores)) {
          return {
            player1: parsed.scores[0] || 0,
            player2: parsed.scores[1] || 0,
            player3: parsed.scores[2] || 0,
            player4: parsed.scores[3] || 0
          };
        }
      }

    } catch (error) {
      this.log.error(`Error parsing score response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Fallback: return zero scores
    this.log.warn('Could not parse score response, returning zero scores');
    return {
      player1: 0,
      player2: 0,
      player3: 0,
      player4: 0
    };
  }

  private async submitTeamScores(gameId: number, players: any[], scores: ControllinoScoreResponse): Promise<void> {
    try {
      // Map controllino scores to players
      const playerScores: PlayerScoreData[] = [
        { playerId: players[0]?.id || 1, playerPoints: scores.player1, isJackpot: false },
        { playerId: players[1]?.id || 2, playerPoints: scores.player2, isJackpot: false },
        { playerId: players[2]?.id || 3, playerPoints: scores.player3, isJackpot: false },
        { playerId: players[3]?.id || 4, playerPoints: scores.player4, isJackpot: false }
      ].filter(p => p.playerPoints > 0); // Only include players with scores

      const scoreRequest: TeamScoreRequest = {
        gameId,
        players: playerScores
      };

      const response = await this.api.createTeamScore(scoreRequest);

      if (response.code === 200) {
        this.log.log('Team scores submitted successfully');
      } else {
        this.log.error(`Failed to submit scores: ${response.message}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Error submitting team scores: ${errorMessage}`);
    }
  }
}
