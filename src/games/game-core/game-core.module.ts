import { Module } from '@nestjs/common';
import { GameEngineService } from './game-engine.service';
import { HardwareModule } from 'src/hardware/hardware.module';
import { ApiModule } from 'src/api/api.module';
import { UiModule } from 'src/ui/ui.module';

const strategies: Provider<GameStrategy>[] = [
  PinballStrategy,
  RollerSkateStrategy,
  PlinkoStrategy,
  SpiralStrategy,
  FortressStrategy,
  SkeeBallStrategy,
  SkyscraperStrategy,
];

const strategyClasses = [
  PinballStrategy,
  RollerSkateStrategy,
  PlinkoStrategy,
  SpiralStrategy,
  FortressStrategy,
  SkeeBallStrategy,
  SkyscraperStrategy,
] as const;

@Module({
  imports: [
    HardwareModule.register(process.env.MODE === 'PROD' ? 'PROD' : 'SIM'), 
    ApiModule,
    UiModule,
  ],
  providers: [
    ...strategyClasses,

    {
      provide: GAME_STRATEGIES,
      useFactory: (...strategies: typeof strategyClasses[number][]) => strategies,
      inject: [...strategyClasses],
    },
    GameEngineService, 
    TimerService
  ],
  exports: [],
})
export class GameCoreModule { }