import { Module } from '@nestjs/common';
import { GameEngineService } from './game-engine.service';
import { HardwareModule } from 'src/hardware/hardware.module';
import { ApiModule } from 'src/api/api.module';
import { UiModule } from 'src/ui/ui.module';

@Module({
  imports: [
    HardwareModule.register(process.env.MODE === 'PROD' ? 'PROD' : 'SIM'),
    ApiModule,
    UiModule,
  ],
  providers: [
    GameEngineService
  ],
  exports: [],
})
export class GameCoreModule { }