import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Observable, Subject } from 'rxjs';
import { SensorBusService } from '../interfaces/sensor-bus.interface';
import { SensorEvent } from '../../common/interfaces/sensor-event.interface';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';

// eslint-disable-next-line @typescript-eslint/no-var-requires
// Dynamically support serialport v9 (constructor expects path first) and v10 (options object)
// eslint-disable-next-line @typescript-eslint/no-var-requires
const SerialPortPkg = require('serialport');
const SerialCtor: any = SerialPortPkg.SerialPort ?? SerialPortPkg;

/*
 * Serial‑line parameters now come from `global.json → hardware.controllino.serial`.
 * Fallbacks ensure legacy defaults still work if the JSON entry is missing.
 */
interface SerialCfg {
  baudRate: number;
  vendorId: string;
  defaultPort: string;
}

@Injectable()
export class ControllinoSensorService
  implements SensorBusService, OnModuleInit
{
  private readonly log = new Logger(ControllinoSensorService.name);

  private port!: any; // SerialPort instance
  private readonly chunks: string[] = [];
  /** Last emit timestamp for every sensor, used for simple debounce */
  private readonly lastHit: Record<string, number> = {};
  /** Minimal delay between two consecutive hits of the *same* sensor */
  private static readonly DEBOUNCE_MS = 120; // ms – tweak to taste
  private readonly ev$ = new Subject<SensorEvent>();

  // Arduino.js compatible variables
  public readonly emitter = new EventEmitter();
  private tempBuffer1 = '';
  private statusCmd1 = '';
  private input1 = 0;
  private fAnswerReceived1 = false;
  private nbrOfComdOngoing = 0;

  constructor(private cfg: ConfigService) {}

  /* ------------------------------------------------------------------ */
  /*  Public API                                                         */
  /* ------------------------------------------------------------------ */

  onEvent(): Observable<SensorEvent> {
    return this.ev$.asObservable();
  }

  /**
   * Send a command to the Controllino
   * @param command The command to send
   */
  sendCommand(command: string): void {
    if (!this.port || !this.port.isOpen) {
      this.log.warn('Cannot send command - serial port not open');
      return;
    }

    // Enhanced logging for different command types
    if (command.startsWith('T')) {
      this.log.log(`Sending team setup command to Controllino: ${command}`);
    } else if (command.startsWith('O04')) {
      this.log.log(`Sending arcade machine command to Controllino: ${command}`);
    } else {
      this.log.debug(`Sending command to Controllino: ${command}`);
    }

    this.port.write(command + '\n'); // Add newline for better parsing on Controllino side
  }

  /**
   * Check if the serial port is open
   */
  isSerialPortOpen(): boolean {
    return this.port && this.port.isOpen;
  }

  async onModuleInit() {
    await this.openSerial();
  }

  /* ------------------------------------------------------------------ */
  /*  Serial initialisation                                             */
  /* ------------------------------------------------------------------ */

  private async openSerial() {
    const serialCfg: SerialCfg = this.cfg.get<SerialCfg>(
      'global.hardware.controllino.serial',
    ) ?? {
      baudRate: 9600,
      vendorId: '2341',
      defaultPort: '/dev/ttyACM0',
    };

    // 1️⃣  Explicit env override still wins (WINDOWS: e.g. "COM9")
    let path = serialCfg.defaultPort;
    if (path) {
      this.log.log(`Using port from env/global.json → ${path}`);
    } else {
      // 2️⃣  Auto‑detect by USB vendorId (works on Linux & macOS; vendorId often undefined on Windows)
      const ports = await SerialPortPkg.list();
      const info = ports.find(
        (p: any) =>
          (p.vendorId ?? '').toLowerCase() === serialCfg.vendorId.toLowerCase(),
      );
      if (!info) {
        throw new Error(
          `Controllino not found. Provide PORT=<COMx> env var or define "defaultPort" in global.json`,
        );
      }
      path = info.path;
    }

    /* open */
    this.port = new SerialCtor(path, {
      baudRate: serialCfg.baudRate,
      autoOpen: false,
    });

    this.port.once('open', () => {
      this.log.log(`Controllino opened on ${path}`);
      // Initial poll to get the current mask
      this.port.write('I');
    });

    this.port.on('data', (buf: Buffer) => this.onChunk(buf));
    this.port.on('error', (err: Error) => this.log.error(err.message));
    this.port.open();
  }

  /* ------------------------------------------------------------------ */
  /*  Parsing                                                           */
  /* ------------------------------------------------------------------ */

  private onChunk(buf: Buffer) {
    // Arduino.js compatible data handling
    this.tempBuffer1 += buf.toString();
    this.manageSerialPort1();
  }

  // Arduino.js compatible serial port management
  private manageSerialPort1() {
    // Expected format Inn
    // Expected format On
    // if I is missing, remove unused data
    while (true) {
      const offsetO = this.tempBuffer1.indexOf('O');
      const offsetI = this.tempBuffer1.indexOf('I');

      // return if nothing to do
      if (offsetO < 0 && offsetI < 0) break;

      // check if answer to Output command
      if (offsetO > offsetI) {
        // manage frame 0
        if (offsetO >= 0) {
          // remove unused data => in case of error or frame not well formatted
          this.tempBuffer1 = this.tempBuffer1.substring(offsetO);
          if (this.tempBuffer1.length > 1) {
            this.statusCmd1 = this.tempBuffer1.substring(1, 2);
            // If statusCmd1 == 1 then send an Success event else Failed event
            if (this.statusCmd1 != '1') this.emitter.emit('cmdFailedEvent');
            this.fAnswerReceived1 = true;
            // answer received, stop timer
            if (this.nbrOfComdOngoing > 1) {
              this.nbrOfComdOngoing--;
            } else {
              this.nbrOfComdOngoing = 0;
            }
            // remove string processed
            this.tempBuffer1 = this.tempBuffer1.substring(2);
          } else break; // string too short, end of processing
        }
      }

      // check if input event
      if (offsetI >= 0) {
        // remove unused data => in case of error or frame not well formatted
        this.tempBuffer1 = this.tempBuffer1.substring(offsetI);
        // console.log(`message debug:` + this.tempBuffer1);
        // event I01x
        if (this.tempBuffer1.length > 6) {
          // new format I21xxyy
          // case of get input answer
          if (this.nbrOfComdOngoing > 1) {
            this.nbrOfComdOngoing--;
          } else {
            this.nbrOfComdOngoing = 0;
          }

          // just to detect answer
          this.statusCmd1 = this.tempBuffer1.substring(0, 1);

          // just to detect answer
          this.statusCmd1 = this.tempBuffer1.substring(1, 3);

          this.manageInputEvent(
            this.tempBuffer1.substring(1, 3),
            this.tempBuffer1.substring(3, 7)
          );
          this.tempBuffer1 = this.tempBuffer1.substring(5);
        } else break; // string too short, end of processing
      }
    }
  }

  // Arduino.js compatible input event management
  private manageInputEvent(mes: string, input: string) {
    const a = parseInt('0x' + input, 16);

    if (mes == '21') {
      // answer to getinput 1
      this.input1 = a;
      this.fAnswerReceived1 = true;
      return;
    }

    if (mes[0] == '0') {
      mes = mes.substring(1);
    }

    if (mes.length > 0) {
      // Emit individual sensor events like arduino.js
      this.emitter.emit('EventInput', mes, a);
      this.log.log(`EventInput → ${mes}, value: ${a}`);

      // Also emit to our existing RxJS stream for backward compatibility
      this.ev$.next({
        id: 'IR' + mes,
        ts: Date.now(),
        data: a.toString()
      });
    }
  }
}
