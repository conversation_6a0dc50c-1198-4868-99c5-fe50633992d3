import { Injectable, Logger } from '@nestjs/common';
import { Observable, Subject } from 'rxjs';
import { NfcReaderService } from '../interfaces/nfc-reader.interface';
import { NFC } from 'nfc-pcsc';

@Injectable()
export class PcscLiteReaderService implements NfcReaderService {
  private readonly tag$ = new Subject<string>();
  private readonly log = new Logger(PcscLiteReaderService.name);

  constructor() {
    this.log.log('Initializing PCSC-Lite NFC reader...');

    try {
      const pcsc = new NFC();

      pcsc.on('reader', reader => {
        this.log.log(`📱 NFC Reader detected: ${reader.name}`);

        reader.on('card', card => {
          this.log.log(`🏷️  NFC Card detected: ${card.uid}`);
          this.tag$.next(card.uid);
        });

        reader.on('error', err => {
          this.log.error(`NFC Reader error: ${err.message}`);
        });

        reader.on('end', () => {
          this.log.warn(`📱 NFC Reader removed: ${reader.name}`);
        });
      });

      pcsc.on('error', err => {
        this.log.error(`PCSC-Lite error: ${err.message}`);
      });

      this.log.log('PCSC-Lite NFC reader initialized successfully');
    } catch (error) {
      this.log.error(`Failed to initialize PCSC-Lite: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  onTag(): Observable<string> {
    return this.tag$.asObservable();
  }
}
