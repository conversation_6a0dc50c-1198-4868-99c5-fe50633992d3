/**
 * Example of how to use the Arduino.js compatible event system
 * 
 * This shows how to listen to individual sensor events exactly like arduino.js
 */

import { ControllinoSensorService } from '../hardware/sensors/controllino-sensor.service';

export class ArduinoJsUsageExample {
  
  constructor(private controllinoService: ControllinoSensorService) {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Arduino.js compatible event listening
    // This matches exactly: arduino.emitter.on('EventInput', (numEvent, input) => {})
    
    this.controllinoService.emitter.on('EventInput', (numEvent: string, input: number) => {
      console.log(`🎯 Arduino.js Event Received:`);
      console.log(`   Event Number: ${numEvent}`);
      console.log(`   Input Value: ${input} (decimal)`);
      console.log(`   Input Value: 0x${input.toString(16).toUpperCase()} (hex)`);
      
      // Handle specific sensor events
      this.handleSensorEvent(numEvent, input);
    });

    // You can also listen to command failures like arduino.js
    this.controllinoService.emitter.on('cmdFailedEvent', (reason?: string) => {
      console.log(`❌ Command Failed: ${reason || 'Unknown reason'}`);
    });
  }

  private handleSensorEvent(eventNumber: string, value: number) {
    switch (eventNumber) {
      case '01':
        console.log(`🔴 Sensor 1 triggered with value: ${value}`);
        break;
      case '02':
        console.log(`🟡 Sensor 2 triggered with value: ${value}`);
        break;
      case '03':
        console.log(`🟢 Sensor 3 triggered with value: ${value}`);
        break;
      case '04':
        console.log(`🔵 Sensor 4 triggered with value: ${value}`);
        break;
      default:
        console.log(`⚪ Unknown sensor ${eventNumber} triggered with value: ${value}`);
    }
  }

  // Example of how to send commands (same as arduino.js)
  public async sendCommand(command: string): Promise<void> {
    console.log(`📤 Sending command: ${command}`);
    this.controllinoService.sendCommand(command);
  }

  // Example usage methods
  public async testSensorReading(): Promise<void> {
    console.log('🧪 Testing sensor reading...');
    
    // Send a command to read inputs (like arduino.js get_input1())
    await this.sendCommand('I');
    
    // The response will come via EventInput event
  }

  public async testOutputControl(): Promise<void> {
    console.log('🧪 Testing output control...');
    
    // Turn on output 1 (like arduino.js set_output(1, 1))
    await this.sendCommand('O01+1');
    
    // Wait 2 seconds
    setTimeout(async () => {
      // Turn off output 1
      await this.sendCommand('O01+0');
    }, 2000);
  }
}

/**
 * Usage in your game logic:
 * 
 * const example = new ArduinoJsUsageExample(controllinoService);
 * 
 * // Listen to events exactly like arduino.js:
 * controllinoService.emitter.on('EventInput', (numEvent, input) => {
 *   console.log(`Sensor ${numEvent} value: ${input}`);
 * });
 * 
 * // Send commands exactly like arduino.js:
 * controllinoService.sendCommand('O01+1'); // Turn on output 1
 * controllinoService.sendCommand('I');     // Read inputs
 */
