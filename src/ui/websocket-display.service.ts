import { Injectable, Logger } from '@nestjs/common';
import { DisplayService } from '../hardware/interfaces/display.interface';
import { DisplayGateway } from './display.gateway';
import { ApiService } from '../api/api.service';

@Injectable()
export class WebsocketDisplayService implements DisplayService {
  private readonly log = new Logger(WebsocketDisplayService.name);
  private sessionTimer: NodeJS.Timeout | null = null;
  private currentTeam: any = null;
  private sessionStartTime: number = 0;
  private maxSessionDuration: number = 0;

  constructor(
    private gateway: DisplayGateway,
    private apiService: ApiService
  ) {}

  /** Send waiting state to connected clients */
  showSplash(title: string, subtitle?: string): void {
    line('🎮  ' + title);
    if (subtitle) console.log(subtitle);
    
    // Send waiting state to WebSocket clients
    this.sendWaitingState(title);
  }

  /** Legacy method - not used in new flow */
  updateScore(score: number): void {
    console.log('[SCORE] ' + score);
  }

  /** Legacy method - not used in new flow */
  updateTimer(sec: number): void {
    console.log('[TIMER] ' + sec + 's left');
  }

  /** Legacy method - replaced by sendGameComplete */
  showResult(serialised: string): void {
    line('🏁 RESULT');
    console.log(serialised);
  }

  // === NEW WEBSOCKET METHODS ===

  /** Send waiting state message */
  sendWaitingState(message: string = 'Scan your team badge'): void {
    this.stopSessionTimer();
    this.currentTeam = null;
    
    try {
      this.gateway.broadcast({ 
        action: 'waiting', 
        message 
      });
      this.log.log(`📡 Sent waiting state: ${message}`);
    } catch (error) {
      this.log.error('Failed to send waiting state', error);
    }
  }

  /** Handle team authorization and start session tracking */
  async sendTeamAuthorized(team: any): Promise<void> {
    try {
      this.log.log(`📡 Team authorized: ${team.name} (ID: ${team.id})`);
      
      // Fetch team session data from API
      const sessionData = await this.apiService.getTeamSession(team.id);
      
      this.currentTeam = team;
      this.sessionStartTime = Date.now();
      this.maxSessionDuration = sessionData.gamePlayDuration || 30; // Default 30 seconds for testing
      
      // Send team authorization message
      this.gateway.broadcast({
        action: 'team_authorized',
        team: team,
        sessionDuration: sessionData.sessionDuration || 0,
        gamePlayDuration: this.maxSessionDuration
      });
      
      this.log.log(`📡 Sent team authorization for ${team.name}, max duration: ${this.maxSessionDuration}s`);
      
      // Start session timer
      this.startSessionTimer();
      
    } catch (error) {
      this.log.error(`Failed to handle team authorization for team ${team.id}:`, error);
      // Fallback: use default values
      this.currentTeam = team;
      this.sessionStartTime = Date.now();
      this.maxSessionDuration = 30; // Default 30 seconds
      
      this.gateway.broadcast({
        action: 'team_authorized',
        team: team,
        sessionDuration: 0,
        gamePlayDuration: this.maxSessionDuration
      });
      
      this.startSessionTimer();
    }
  }

  /** Send game completion with scores */
  sendGameComplete(scores: any[]): void {
    this.stopSessionTimer();
    
    try {
      this.gateway.broadcast({
        action: 'game_complete',
        scores: scores,
        team: this.currentTeam
      });
      
      this.log.log(`📡 Sent game complete with ${scores.length} scores`);
      
      // Return to waiting state after 5 seconds
      setTimeout(() => {
        this.sendWaitingState();
      }, 5000);
      
    } catch (error) {
      this.log.error('Failed to send game complete', error);
    }
  }

  // === PRIVATE TIMER METHODS ===

  private startSessionTimer(): void {
    this.stopSessionTimer(); // Clear any existing timer
    
    this.sessionTimer = setInterval(() => {
      const currentDuration = Math.floor((Date.now() - this.sessionStartTime) / 1000);
      
      if (currentDuration >= this.maxSessionDuration) {
        this.stopSessionTimer();
        return;
      }
      
      try {
        this.gateway.broadcast({
          action: 'session_tick',
          currentDuration: currentDuration,
          maxDuration: this.maxSessionDuration,
          team: this.currentTeam
        });
        
        this.log.debug(`📡 Session tick: ${currentDuration}/${this.maxSessionDuration}s`);
      } catch (error) {
        this.log.error('Failed to send session tick', error);
      }
    }, 1000); // Every second
  }

  private stopSessionTimer(): void {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = null;
    }
  }
}

function line(title: string) {
  console.log('='.repeat(40));
  console.log(title);
  console.log('='.repeat(40));
}
