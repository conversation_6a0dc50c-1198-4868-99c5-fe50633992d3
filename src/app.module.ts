import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import globalConfig from './config/global';
import validationSchema from './config/validation.schema';
import { HardwareModule } from './hardware/hardware.module';
import { ApiModule } from './api/api.module';
import { UiModule } from './ui/ui.module';
import { GameCoreModule } from './games/game-core/game-core.module';
import { SimulationModule } from './simulation/mocks/simulation.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [globalConfig],
      validationSchema,
    }),
    HardwareModule.register('PROD'), // Temporarily hardcoded for testing
    // Only import SimulationModule in SIM mode
    ...(false ? [ // Temporarily disabled
      SimulationModule.registerAsync({
        imports: [ConfigModule],
        useFactory: (cfg: ConfigService) => ({ mode: cfg.get('mode') }),
        inject: [ConfigService],
      })
    ] : []),
    ApiModule,
    UiModule,
    GameCoreModule,
  ],
})
export class AppModule {}