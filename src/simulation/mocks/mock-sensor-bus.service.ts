import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SensorBusService } from '../../hardware/interfaces/sensor-bus.interface';
import { SensorEvent } from '../../common/interfaces/sensor-event.interface';
import { Subject, Observable } from 'rxjs';

@Injectable()
export class MockSensorBusService implements SensorBusService {
  private readonly log = new Logger(MockSensorBusService.name);
  private readonly ev$ = new Subject<SensorEvent>();
  private teamPlayers: string[] = []; // Store badge IDs for score generation
  private gameInProgress = false;

  constructor(private readonly cfg: ConfigService) {
    // No keypress handling needed - controllino only sends score responses
  }

  onEvent(): Observable<SensorEvent> {
    return this.ev$.asObservable();
  }

  // Mock implementation of sendCommand for simulation
  sendCommand(command: string): void {
    this.log.log(`[MOCK] Received command: ${command}`);

    // Handle team setup command: T<PLAYER_COUNT>:<BADGE1>,<BADGE2>,...
    if (command.startsWith('T')) {
      this.handleTeamSetupCommand(command);
    }
    // Handle arcade machine commands: O04+1 (start), O04+0 (stop)
    else if (command.startsWith('O04')) {
      this.handleArcadeMachineCommand(command);
    }
    // Handle other output commands (LEDs, etc.)
    else if (command.startsWith('O')) {
      this.log.log(`[MOCK] Output command: ${command}`);
    }
  }

  private handleTeamSetupCommand(command: string): void {
    // Parse: T2:A001,A002 or T3:A001,A002,A003
    const match = command.match(/^T(\d+):(.+)$/);
    if (match) {
      const playerCount = parseInt(match[1]);
      const badgeIds = match[2].split(',').map(id => id.trim());

      this.teamPlayers = badgeIds;
      this.log.log(`[MOCK] Team setup: ${playerCount} players with badges: ${badgeIds.join(', ')}`);
    } else {
      this.log.warn(`[MOCK] Invalid team setup command: ${command}`);
    }
  }

  private handleArcadeMachineCommand(command: string): void {
    if (command === 'O04+1') {
      this.log.log('[MOCK] Arcade machine started - game in progress');
      this.gameInProgress = true;
      this.startGameSimulation();
    } else if (command === 'O04+0') {
      this.log.log('[MOCK] Arcade machine stopped');
      this.gameInProgress = false;
    }
  }

  private startGameSimulation(): void {
    if (this.teamPlayers.length === 0) {
      this.log.warn('[MOCK] No team players set - cannot simulate game');
      return;
    }

    this.log.log(`[MOCK] Starting 15-second game simulation for ${this.teamPlayers.length} players`);

    // Simulate game completion after 15 seconds
    setTimeout(() => {
      if (this.gameInProgress) {
        this.generateScoreResponse();
      }
    }, 15000);
  }

  private generateScoreResponse(): void {
    // Get jackpot threshold from config
    const jackpotThreshold = this.cfg.get<number>('game.jackpotThreshold') || 1000;

    // Generate scores for each player: SCORE:<BADGE1>:<POINTS1>,<BADGE2>:<POINTS2>,...
    const scoreEntries = this.teamPlayers.map(badgeId => {
      // 20% chance of jackpot score (above threshold), 80% normal score
      const isJackpotRoll = Math.random() < 0.2;

      let points: number;
      if (isJackpotRoll) {
        // Jackpot: threshold + random 0-500 (e.g., 1000-1500)
        points = jackpotThreshold + Math.floor(Math.random() * 500);
        this.log.log(`[MOCK] 🎰 Jackpot generated for ${badgeId}: ${points} points!`);
      } else {
        // Normal score: 100 to (threshold - 1)
        const maxNormal = jackpotThreshold - 1;
        points = Math.floor(Math.random() * (maxNormal - 100 + 1)) + 100;
      }

      return `${badgeId}:${points}`;
    });

    const scoreData = `SCORE:${scoreEntries.join(',')}`;
    this.log.log(`[MOCK] Generated score response: ${scoreData}`);

    // Emit the score event
    this.ev$.next({
      id: 'SCORE',
      ts: Date.now(),
      data: scoreData
    });
  }
}