import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SensorBusService } from '../../hardware/interfaces/sensor-bus.interface';
import { SensorEvent } from '../../common/interfaces/sensor-event.interface';
import { Subject, Observable } from 'rxjs';
import { EventEmitter } from 'events';

@Injectable()
export class MockSensorBusService implements SensorBusService {
  private readonly log = new Logger(MockSensorBusService.name);
  private readonly ev$ = new Subject<SensorEvent>();
  private teamPlayers: string[] = []; // Store badge IDs for score generation
  private gameInProgress = false;

  // Arduino.js compatible EventEmitter
  public readonly emitter = new EventEmitter();

  constructor(private readonly cfg: ConfigService) {
    // No keypress handling needed - controllino only sends score responses
  }

  onEvent(): Observable<SensorEvent> {
    return this.ev$.asObservable();
  }

  // Mock implementation of sendCommand for simulation
  sendCommand(command: string): void {
    this.log.log(`[MOCK] Received command: ${command}`);

    // Get config values for dynamic command recognition
    const arcadeMachine = this.cfg.get<string>('hardware.controllino.outputs.arcadeMachine') || '04';
    const redLed = this.cfg.get<string>('hardware.controllino.outputs.redLed') || '01';
    const greenLed = this.cfg.get<string>('hardware.controllino.outputs.greenLed') || '02';
    const door = this.cfg.get<string>('hardware.controllino.outputs.door') || '05';
    const cellLight = this.cfg.get<string>('hardware.controllino.outputs.cellLight') || '99';

    // Handle arcade machine commands
    if (command.startsWith(`O${arcadeMachine}`)) {
      this.handleArcadeMachineCommand(command);
    }
    // Handle LED commands
    else if (command.startsWith(`O${redLed}`) || command.startsWith(`O${greenLed}`)) {
      this.log.log(`[MOCK] LED command: ${command}`);
    }
    // Handle cell light commands
    else if (command.startsWith(`O${cellLight}`)) {
      this.log.log(`[MOCK] Cell light command: ${command}`);
    }
    // Handle door commands
    else if (command.startsWith(`O${door}`)) {
      this.log.log(`[MOCK] Door command: ${command}`);
    }
    // Handle other output commands
    else if (command.startsWith('O')) {
      this.log.log(`[MOCK] Output command: ${command}`);
    }
  }

  // Mock implementation of binary command for simulation
  sendBinaryCommand(buffer: Buffer): void {
    this.log.log(`[MOCK] Received binary command: ${buffer.toString('hex').toUpperCase()}`);

    // Check if this is team setup command: A01 + UIDs
    if (buffer.length === 19 && buffer[0] === 0x41 && buffer[1] === 0x30 && buffer[2] === 0x31) {
      this.handleBinaryTeamSetupCommand(buffer);
    }
  }

  private handleBinaryTeamSetupCommand(buffer: Buffer): void {
    // Parse binary team setup: A01 + 4 UIDs (4 bytes each)
    this.teamPlayers = [];

    this.log.log(`[MOCK] Binary team setup command received:`);

    for (let i = 0; i < 4; i++) {
      const startIndex = 3 + (i * 4);
      const uid = buffer.subarray(startIndex, startIndex + 4);
      const hexUid = uid.toString('hex').toUpperCase();

      // Check if UID is not all zeros (padding)
      if (uid.some(byte => byte !== 0x00)) {
        this.teamPlayers.push(hexUid);
        this.log.log(`[MOCK] Player ${i + 1}: ${hexUid}`);
      } else {
        this.log.log(`[MOCK] Player ${i + 1}: [EMPTY - padded]`);
      }
    }

    this.log.log(`[MOCK] Team setup complete: ${this.teamPlayers.length} players`);
  }

  private handleArcadeMachineCommand(command: string): void {
    // Get config values for dynamic command recognition
    const arcadeMachine = this.cfg.get<string>('hardware.controllino.outputs.arcadeMachine') || '04';
    const stateOn = this.cfg.get<string>('hardware.controllino.states.on') || '1';
    const stateOff = this.cfg.get<string>('hardware.controllino.states.off') || '0';

    if (command === `O${arcadeMachine}+${stateOn}`) {
      this.log.log('[MOCK] Arcade machine started - game in progress');
      this.gameInProgress = true;
      this.startGameSimulation();
    } else if (command === `O${arcadeMachine}+${stateOff}`) {
      this.log.log('[MOCK] Arcade machine stopped');
      this.gameInProgress = false;
    }
  }

  private startGameSimulation(): void {
    if (this.teamPlayers.length === 0) {
      this.log.warn('[MOCK] No team players set - cannot simulate game');
      return;
    }

    this.log.log(`[MOCK] Starting 15-second game simulation for ${this.teamPlayers.length} players`);

    // Simulate game completion after 15 seconds
    setTimeout(() => {
      if (this.gameInProgress) {
        this.generateScoreResponse();
      }
    }, 15000);
  }

  private generateScoreResponse(): void {
    // Get jackpot threshold from config
    const jackpotThreshold = this.cfg.get<number>('game.jackpotThreshold') || 1000;

    this.log.log(`[MOCK] Generating individual score events for ${this.teamPlayers.length} players`);

    // Generate individual EventInput events for each player (1-4)
    for (let i = 0; i < this.teamPlayers.length; i++) {
      const playerOrder = (i + 1).toString(); // 1, 2, 3, 4

      // 20% chance of jackpot score (above threshold), 80% normal score
      const isJackpotRoll = Math.random() < 0.2;

      let points: number;
      if (isJackpotRoll) {
        // Jackpot: threshold + random 0-500 (e.g., 1000-1500)
        points = jackpotThreshold + Math.floor(Math.random() * 500);
        this.log.log(`[MOCK] 🎰 Jackpot generated for Player ${playerOrder}: ${points} points!`);
      } else {
        // Normal score: 100 to (threshold - 1)
        const maxNormal = jackpotThreshold - 1;
        points = Math.floor(Math.random() * (maxNormal - 100 + 1)) + 100;
      }

      // Emit individual EventInput event with slight delay
      setTimeout(() => {
        this.emitter.emit('EventInput', playerOrder, points);
        this.log.log(`[MOCK] EventInput emitted: Player ${playerOrder}, Score: ${points}`);

        // Also emit to RxJS stream for backward compatibility
        this.ev$.next({
          id: `PLAYER${playerOrder}`,
          ts: Date.now(),
          data: points.toString()
        });
      }, i * 200); // 200ms delay between each player
    }
  }
}