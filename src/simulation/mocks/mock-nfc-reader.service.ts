import { Injectable } from '@nestjs/common';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { Subject, Observable } from 'rxjs';
import * as readline from 'node:readline';

@Injectable()
export class MockNfcReaderService implements NfcReaderService {
  private readonly tag$ = new Subject<string>();
  private rl!: readline.Interface;

  constructor() {
    // Only show simulation controls if we're actually in simulation mode
    if (process.env.MODE !== 'PROD') {
      console.log('🎮 NFC Simulation Controls:');
      console.log('  Press "b" + Enter to enter a badge ID');
      console.log('  Press Ctrl+C to exit');
      this.setupSimpleInput();
    }
  }

  private setupSimpleInput(): void {
    // Create a single persistent readline interface
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: ''
    });

    // Handle line input (when user presses Enter)
    this.rl.on('line', (input) => {
      const trimmed = input.trim().toLowerCase();

      if (trimmed === 'b') {
        this.promptForBadgeId();
      } else if (trimmed.length > 0) {
        // Treat any other input as a badge ID
        console.log(`📱 Simulating badge scan: ${trimmed.toUpperCase()}`);
        this.tag$.next(trimmed.toUpperCase());
        console.log('\n🎮 Press "b" + Enter to enter another badge ID');
      }
    });

    // Handle Ctrl+C
    this.rl.on('SIGINT', () => {
      console.log('\n👋 Exiting...');
      process.exit();
    });

    console.log('\n🎮 Press "b" + Enter to enter a badge ID');
  }

  private promptForBadgeId(): void {
    this.rl.question('\n📱 Enter badge ID: ', (badgeId) => {
      const cleanBadgeId = badgeId.trim().toUpperCase();

      if (cleanBadgeId) {
        console.log(`📱 Simulating badge scan: ${cleanBadgeId}`);
        this.tag$.next(cleanBadgeId);
      } else {
        console.log('❌ No badge ID entered');
      }

      console.log('\n🎮 Press "b" + Enter to enter another badge ID');
    });
  }

  onTag(): Observable<string> {
    return this.tag$.asObservable();
  }
}