import { Injectable } from '@nestjs/common';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { Subject, Observable } from 'rxjs';
import * as readline from 'node:readline';

@Injectable()
export class MockNfcReaderService implements NfcReaderService {
  private readonly tag$ = new Subject<string>();
  constructor() {
    readline.emitKeypressEvents(process.stdin);
    if (process.stdin.setRawMode) process.stdin.setRawMode(true);

    console.log('🎮 NFC Simulation Controls:');
    console.log('  Press "s" to scan badge: 123456');
    console.log('  Press "b" to scan badge: 1234');
    console.log('  Press Ctrl+C to exit');

    process.stdin.on('keypress', (_, key) => {
      if (key.name === 's') {
        console.log('📱 Simulating badge scan: 123456');
        this.tag$.next('123456');
      } else if (key.name === 'b') {
        console.log('📱 Simulating badge scan: 1234');
        this.tag$.next('1234');
      }
    });
  }
  onTag(): Observable<string> {
    return this.tag$.asObservable();
  }
}