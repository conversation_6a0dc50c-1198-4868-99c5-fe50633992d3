import { Injectable } from '@nestjs/common';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { Subject, Observable } from 'rxjs';
import * as readline from 'node:readline';

@Injectable()
export class MockNfcReaderService implements NfcReaderService {
  private readonly tag$ = new Subject<string>();
  private rl: readline.Interface | null = null;
  private isWaitingForInput = false;

  constructor() {
    console.log('🎮 NFC Simulation Controls:');
    console.log('  Press "b" to enter a badge ID');
    console.log('  Press Ctrl+C to exit');

    this.setupKeyListener();
  }

  private setupKeyListener(): void {
    readline.emitKeypressEvents(process.stdin);
    if (process.stdin.setRawMode) process.stdin.setRawMode(true);

    process.stdin.on('keypress', (_, key) => {
      // Always allow Ctrl+C to work
      if (key && key.ctrl && key.name === 'c') {
        console.log('\n👋 Exiting...');
        process.exit();
      }

      // Only handle 'b' if not currently waiting for input
      if (!this.isWaitingForInput && key && key.name === 'b') {
        this.promptForBadgeId();
      }
    });
  }

  private promptForBadgeId(): void {
    this.isWaitingForInput = true;

    // Create a new readline interface for this input session
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    // Temporarily disable raw mode to allow normal input
    if (process.stdin.setRawMode) process.stdin.setRawMode(false);

    this.rl.question('\n📱 Enter badge ID: ', (badgeId) => {
      const cleanBadgeId = badgeId.trim();

      if (cleanBadgeId) {
        console.log(`📱 Simulating badge scan: ${cleanBadgeId}`);
        this.tag$.next(cleanBadgeId);
      } else {
        console.log('❌ No badge ID entered');
      }

      // Close the readline interface
      if (this.rl) {
        this.rl.close();
        this.rl = null;
      }

      // Small delay before re-enabling raw mode to prevent interference
      setTimeout(() => {
        // Re-enable raw mode for keypress detection
        if (process.stdin.setRawMode) process.stdin.setRawMode(true);
        this.isWaitingForInput = false;
        console.log('\n🎮 Press "b" to enter another badge ID');
      }, 200); // Increased delay to ensure proper cleanup
    });
  }

  onTag(): Observable<string> {
    return this.tag$.asObservable();
  }
}