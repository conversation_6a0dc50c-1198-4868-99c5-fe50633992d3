import { Injectable } from '@nestjs/common';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { Subject, Observable } from 'rxjs';
import * as readline from 'node:readline';

@Injectable()
export class MockNfcReaderService implements NfcReaderService {
  private readonly tag$ = new Subject<string>();
  private rl: readline.Interface;
  private isWaitingForInput = false;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    readline.emitKeypressEvents(process.stdin);
    if (process.stdin.setRawMode) process.stdin.setRawMode(true);

    console.log('🎮 NFC Simulation Controls:');
    console.log('  Press "b" to enter a badge ID');
    console.log('  Press Ctrl+C to exit');

    process.stdin.on('keypress', (_, key) => {
      if (key.name === 'b' && !this.isWaitingForInput) {
        this.promptForBadgeId();
      } else if (key.ctrl && key.name === 'c') {
        process.exit();
      }
    });
  }

  private promptForBadgeId(): void {
    this.isWaitingForInput = true;

    // Temporarily disable raw mode to allow normal input
    if (process.stdin.setRawMode) process.stdin.setRawMode(false);

    this.rl.question('\n📱 Enter badge ID: ', (badgeId) => {
      if (badgeId.trim()) {
        console.log(`📱 Simulating badge scan: ${badgeId.trim()}`);
        this.tag$.next(badgeId.trim());
      } else {
        console.log('❌ No badge ID entered');
      }

      // Re-enable raw mode for keypress detection
      if (process.stdin.setRawMode) process.stdin.setRawMode(true);
      this.isWaitingForInput = false;

      console.log('\n🎮 Press "b" to enter another badge ID');
    });
  }

  onTag(): Observable<string> {
    return this.tag$.asObservable();
  }
}