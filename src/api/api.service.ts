// src/api/api.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import {
  TeamGameManagerResponse,
  TeamScoreRequest,
  GameManagerDTO // Legacy - to be removed after refactoring
} from '../common/dto/game‑manager.dto';

@Injectable()
export class ApiService {
  private log = new Logger(ApiService.name);

  constructor(private http: HttpService, private cfg: ConfigService) {}

  private baseUrl() {
    return this.cfg.get<string>('global.api.baseUrl');
  }

  /** ➜ GET /authorization */
  /** GET /api/game-manager/team-authorization */
  async authorizeTeam(badgeId: string, gameId: number): Promise<TeamGameManagerResponse> {
    const url = `${this.baseUrl()}/team-authorization`;
    this.log.log(`[TEAM_AUTH] Requesting authorization for badge: ${badgeId}, game: ${gameId}`);
    this.log.log(`[TEAM_AUTH] Full URL: ${url}?badgeId=${badgeId}&gameId=${gameId}`);

    try {
      const response = await firstValueFrom(
        this.http.get<TeamGameManagerResponse>(url, {
          params: { badgeId, gameId },
          headers: { 'Accept': 'application/json' }
        }),
      );

      this.log.log(`[TEAM_AUTH] HTTP Status: ${response.status}`);
      this.log.log(`[TEAM_AUTH] Raw Response: ${JSON.stringify(response.data, null, 2)}`);
      this.log.log(`[TEAM_AUTH] Response code: ${response.data.code}, team: ${response.data.team?.name || 'null'}`);

      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`[TEAM_AUTH] API Error: ${errorMessage}`);

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        this.log.error(`[TEAM_AUTH] HTTP Status: ${axiosError.response?.status}`);
        this.log.error(`[TEAM_AUTH] Error Response: ${JSON.stringify(axiosError.response?.data, null, 2)}`);
      }
      throw error;
    }
  }

  /** @deprecated Use authorizeTeam instead */
  async authorize(badgeId: string, gameId: number): Promise<GameManagerDTO> {
    const url = `${this.baseUrl()}/authorization`;
    const { data } = await firstValueFrom(
      this.http.get<GameManagerDTO>(url, { params: { badgeId, gameId } }),
    );
    return data;
  }

  /** ➜ GET /create-score */
  /** POST /api/game-manager/team-create-score */
  async createTeamScore(scoreRequest: TeamScoreRequest): Promise<TeamGameManagerResponse> {
    const url = `${this.baseUrl()}/team-create-score`;
    this.log.log(`[TEAM_SCORE] Creating score for game: ${scoreRequest.gameId}, players: ${scoreRequest.players.length}`);
    this.log.log(`[TEAM_SCORE] Full URL: ${url}`);
    this.log.log(`[TEAM_SCORE] Request Body: ${JSON.stringify(scoreRequest, null, 2)}`);

    try {
      const response = await firstValueFrom(
        this.http.post<TeamGameManagerResponse>(url, scoreRequest, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }),
      );

      this.log.log(`[TEAM_SCORE] HTTP Status: ${response.status}`);
      this.log.log(`[TEAM_SCORE] Raw Response: ${JSON.stringify(response.data, null, 2)}`);
      this.log.log(`[TEAM_SCORE] Response code: ${response.data.code}, team points: ${response.data.team?.points || 'null'}`);

      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`[TEAM_SCORE] API Error: ${errorMessage}`);

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        this.log.error(`[TEAM_SCORE] HTTP Status: ${axiosError.response?.status}`);
        this.log.error(`[TEAM_SCORE] Error Response: ${JSON.stringify(axiosError.response?.data, null, 2)}`);
      }
      throw error;
    }
  }

  /** @deprecated Use createTeamScore instead */
  async createScore(
    playerId: number,
    gameId: number,
    playerPoints: number,
  ): Promise<GameManagerDTO> {
    const url = `${this.baseUrl()}/create-score`;
    const { data } = await firstValueFrom(
      this.http.get<GameManagerDTO>(url, {
        params: { playerId, gameId, playerPoints },
      }),
    );
    return data;
  }
}
