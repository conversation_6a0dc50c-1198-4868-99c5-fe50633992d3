// src/api/api.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import {
  TeamGameManagerResponse,
  TeamScoreRequest,
  GameManagerDTO // Legacy - to be removed after refactoring
} from '../common/dto/game‑manager.dto';

@Injectable()
export class ApiService {
  private log = new Logger(ApiService.name);

  constructor(private http: HttpService, private cfg: ConfigService) {}

  private baseUrl() {
    return this.cfg.get<string>('global.api.baseUrl');
  }

  /** ➜ GET /authorization */
  /** GET /api/game-manager/team-authorization */
  async authorizeTeam(badgeId: string, gameId: number): Promise<TeamGameManagerResponse> {
    const url = `${this.baseUrl()}/team-authorization`;
    this.log.log(`[TEAM_AUTH] Requesting authorization for badge: ${badgeId}, game: ${gameId}`);

    const { data } = await firstValueFrom(
      this.http.get<TeamGameManagerResponse>(url, {
        params: { badgeId, gameId },
        headers: { 'Accept': 'application/json' }
      }),
    );

    this.log.log(`[TEAM_AUTH] Response code: ${data.code}, team: ${data.team?.name || 'null'}`);
    return data;
  }

  /** @deprecated Use authorizeTeam instead */
  async authorize(badgeId: string, gameId: number): Promise<GameManagerDTO> {
    const url = `${this.baseUrl()}/authorization`;
    const { data } = await firstValueFrom(
      this.http.get<GameManagerDTO>(url, { params: { badgeId, gameId } }),
    );
    return data;
  }

  /** ➜ GET /create-score */
  /** POST /api/game-manager/team-create-score */
  async createTeamScore(scoreRequest: TeamScoreRequest): Promise<TeamGameManagerResponse> {
    const url = `${this.baseUrl()}/team-create-score`;
    this.log.log(`[TEAM_SCORE] Creating score for game: ${scoreRequest.gameId}, players: ${scoreRequest.players.length}`);

    const { data } = await firstValueFrom(
      this.http.post<TeamGameManagerResponse>(url, scoreRequest, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }),
    );

    this.log.log(`[TEAM_SCORE] Response code: ${data.code}, team points: ${data.team?.points || 'null'}`);
    return data;
  }

  /** @deprecated Use createTeamScore instead */
  async createScore(
    playerId: number,
    gameId: number,
    playerPoints: number,
  ): Promise<GameManagerDTO> {
    const url = `${this.baseUrl()}/create-score`;
    const { data } = await firstValueFrom(
      this.http.get<GameManagerDTO>(url, {
        params: { playerId, gameId, playerPoints },
      }),
    );
    return data;
  }
}
